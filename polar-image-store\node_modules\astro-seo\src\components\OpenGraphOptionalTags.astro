---
const { optional } = Astro.props.openGraph;
---

{optional.audio ? <meta property="og:audio" content={optional.audio} /> : null}
{
  optional.description ? (
    <meta property="og:description" content={optional.description} />
  ) : null
}
{
  optional.determiner ? (
    <meta property="og:determiner" content={optional.determiner} />
  ) : null
}
{
  optional.locale ? (
    <meta property="og:locale" content={optional.locale} />
  ) : null
}
{
  optional.localeAlternate?.map((locale: string) => (
    <meta property="og:locale:alternate" content={locale} />
  ))
}
{
  optional.siteName ? (
    <meta property="og:site_name" content={optional.siteName} />
  ) : null
}
{optional.video ? <meta property="og:video" content={optional.video} /> : null}
