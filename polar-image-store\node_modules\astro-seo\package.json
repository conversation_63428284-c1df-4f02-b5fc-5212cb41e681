{"name": "astro-seo", "version": "0.8.4", "description": "Makes it easy to add SEO relevant tags to your Astro app.", "homepage": "https://github.com/jonasm<PERSON>lin/astro-seo#readme", "bugs": {"url": "https://github.com/jonasm<PERSON>lin/astro-seo/issues"}, "repository": {"type": "git", "url": "https://github.com/jonasm<PERSON><PERSON>/astro-seo"}, "files": ["index.ts", "src/index.ts", "src/SEO.astro", "src/components"], "exports": "./index.ts", "keywords": ["astro-component", "seo"], "author": "<PERSON>", "license": "MIT", "scripts": {"cypress:open": "cypress open", "dev": "astro dev", "build": "astro check && tsc --noEmit && astro build", "start": "astro preview", "format": "prettier -w ./src", "check:format": "prettier -c ./src", "release": "standard-version", "check": "astro check"}, "devDependencies": {"astro": "^4.4.0", "cypress": "^13.0.0", "prettier": "^3.0.3", "prettier-plugin-astro": "^0.12.0", "standard-version": "^9.5.0", "typescript": "^5.3.3"}, "dependencies": {"@astrojs/check": "^0.5.4"}}