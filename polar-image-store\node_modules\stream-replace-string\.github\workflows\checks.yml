# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Checks

on:
  push:
    branches: [master]
  pull_request: 
    branches: [master]

jobs:
  Lint:
  
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v1
      with:
        node-version: 18
    - name: Install Dependencies
      run: npm ci
    - name: Check Lint
      run: npm run lint
  
  Test:
  
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [14, 16, 18, 19]

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
    - name: Install Dependencies
      run: npm ci
    - name: Run Tests
      run: npm test
