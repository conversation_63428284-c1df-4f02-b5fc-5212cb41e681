globalThis.process ??= {}; globalThis.process.env ??= {};
import { g as decodeKey } from './chunks/astro/server_oCxQtzs6.mjs';
import './chunks/astro-designed-error-pages_Bl8yq204.mjs';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/noop-middleware_DQJXsadJ.mjs';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///D:/code/image/polar-image-store/","cacheDir":"file:///D:/code/image/polar-image-store/node_modules/.astro/","outDir":"file:///D:/code/image/polar-image-store/dist/","srcDir":"file:///D:/code/image/polar-image-store/src/","publicDir":"file:///D:/code/image/polar-image-store/public/","buildClientDir":"file:///D:/code/image/polar-image-store/dist/","buildServerDir":"file:///D:/code/image/polar-image-store/dist/_worker.js/","adapterName":"@astrojs/cloudflare","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"about/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/about","isIndex":false,"type":"page","pattern":"^\\/about\\/?$","segments":[[{"content":"about","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/about.astro","pathname":"/about","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"privacy/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/privacy","isIndex":false,"type":"page","pattern":"^\\/privacy\\/?$","segments":[[{"content":"privacy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/privacy.astro","pathname":"/privacy","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"products/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/products","isIndex":true,"type":"page","pattern":"^\\/products\\/?$","segments":[[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/products/index.astro","pathname":"/products","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"success/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/success","isIndex":false,"type":"page","pattern":"^\\/success\\/?$","segments":[[{"content":"success","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/success.astro","pathname":"/success","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"terms/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/terms","isIndex":false,"type":"page","pattern":"^\\/terms\\/?$","segments":[[{"content":"terms","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/terms.astro","pathname":"/terms","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/checkout","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/checkout\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"checkout","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/checkout.ts","pathname":"/api/checkout","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/products","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/products\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/products.ts","pathname":"/api/products","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/webhooks","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/webhooks\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"webhooks","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/webhooks.ts","pathname":"/api/webhooks","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"https://polar-image-store.pages.dev","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["D:/code/image/polar-image-store/src/pages/about.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/privacy.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/[slug].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/success.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/terms.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000@astro-page:node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint@_@js":"pages/_image.astro.mjs","\u0000@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\u0000@astro-page:src/pages/api/checkout@_@ts":"pages/api/checkout.astro.mjs","\u0000@astro-page:src/pages/api/products@_@ts":"pages/api/products.astro.mjs","\u0000@astro-page:src/pages/api/webhooks@_@ts":"pages/api/webhooks.astro.mjs","\u0000@astro-page:src/pages/privacy@_@astro":"pages/privacy.astro.mjs","\u0000@astro-page:src/pages/products/index@_@astro":"pages/products.astro.mjs","\u0000@astro-page:src/pages/success@_@astro":"pages/success.astro.mjs","\u0000@astro-page:src/pages/terms@_@astro":"pages/terms.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"index.js","\u0000@astro-page:src/pages/products/[slug]@_@astro":"pages/products/_slug_.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","D:/code/image/polar-image-store/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_D1_czOH9.mjs","D:/code/image/polar-image-store/node_modules/unstorage/drivers/cloudflare-kv-binding.mjs":"chunks/cloudflare-kv-binding_DMly_2Gl.mjs","\u0000@astrojs-manifest":"manifest_wScRHXKh.mjs","D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts":"_astro/Layout.astro_astro_type_script_index_0_lang.C-iV-8kj.js","D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts":"_astro/Hero.astro_astro_type_script_index_0_lang.CGTWZgqe.js","D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts":"_astro/ImageGallery.astro_astro_type_script_index_0_lang.dQDmp6mo.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const s=document.getElementById(\"mobile-menu-button\"),o=document.getElementById(\"mobile-menu\");s&&o&&s.addEventListener(\"click\",()=>{o.classList.toggle(\"hidden\")});const i=document.getElementById(\"productSearch\"),r=document.getElementById(\"mobileProductSearch\"),t=document.getElementById(\"searchResults\");function d(e){const n=e.value.trim();n.length>2?t&&(t.classList.remove(\"hidden\"),t.innerHTML=`\n            <div class=\"p-4 text-center text-primary-600\">\n              <div class=\"flex items-center justify-center gap-2\">\n                <svg class=\"animate-spin w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                </svg>\n                <span class=\"text-sm\">Searching for \"${n}\"...</span>\n              </div>\n            </div>\n          `,setTimeout(()=>{t&&!t.classList.contains(\"hidden\")&&(t.innerHTML=`\n                <div class=\"p-4\">\n                  <div class=\"text-sm text-primary-600 mb-2\">Search results for \"${n}\"</div>\n                  <a href=\"/products\" class=\"block p-3 hover:bg-primary-50 rounded-lg transition-colors\">\n                    <div class=\"text-primary-900 font-medium\">View all products</div>\n                    <div class=\"text-primary-600 text-sm\">Browse our complete collection</div>\n                  </a>\n                </div>\n              `)},500)):t&&t.classList.add(\"hidden\")}i&&(i.addEventListener(\"input\",e=>d(e.target)),i.addEventListener(\"keydown\",e=>{if(e.key===\"Enter\"){e.preventDefault();const n=e.target.value.trim();n&&(window.location.href=`/products?search=${encodeURIComponent(n)}`)}})),r&&r.addEventListener(\"keydown\",e=>{if(e.key===\"Enter\"){e.preventDefault();const n=e.target.value.trim();n&&(window.location.href=`/products?search=${encodeURIComponent(n)}`)}}),document.addEventListener(\"click\",e=>{t&&!i?.contains(e.target)&&!t.contains(e.target)&&t.classList.add(\"hidden\")})});"],["D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const e=document.getElementById(\"categoryScroll\");if(e){let r=!1,o,c;e.addEventListener(\"touchstart\",t=>{r=!0,o=t.touches[0].pageX-e.offsetLeft,c=e.scrollLeft}),e.addEventListener(\"touchend\",()=>{r=!1}),e.addEventListener(\"touchmove\",t=>{if(!r)return;const s=(t.touches[0].pageX-e.offsetLeft-o)*2;e.scrollLeft=c-s}),e.addEventListener(\"mousedown\",t=>{r=!0,o=t.pageX-e.offsetLeft,c=e.scrollLeft,e.style.cursor=\"grabbing\"}),e.addEventListener(\"mouseleave\",()=>{r=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mouseup\",()=>{r=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mousemove\",t=>{if(!r)return;t.preventDefault();const s=(t.pageX-e.offsetLeft-o)*2;e.scrollLeft=c-s}),e.style.cursor=\"grab\";const i=document.querySelectorAll(\".category-tab\");i.forEach(t=>{t.addEventListener(\"click\",()=>{i.forEach(s=>{s.classList.remove(\"bg-accent-600\",\"text-white\",\"shadow-md\"),s.classList.add(\"bg-primary-50\",\"text-primary-700\",\"hover:bg-primary-100\",\"hover:text-primary-900\");const n=s.querySelector(\"span\");n&&(n.classList.remove(\"bg-white/20\",\"text-white\"),n.classList.add(\"bg-primary-200\",\"text-primary-600\"))}),t.classList.remove(\"bg-primary-50\",\"text-primary-700\",\"hover:bg-primary-100\",\"hover:text-primary-900\"),t.classList.add(\"bg-accent-600\",\"text-white\",\"shadow-md\");const a=t.querySelector(\"span\");a&&(a.classList.remove(\"bg-primary-200\",\"text-primary-600\"),a.classList.add(\"bg-white/20\",\"text-white\"))})})}});"],["D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts","let o=0,n=[];document.addEventListener(\"DOMContentLoaded\",function(){const e=document.querySelectorAll(\".thumbnail img\");if(n=Array.from(e).map(t=>t.src),n.length===0){const t=document.getElementById(\"mainImage\");t&&(n=[t.src])}});function c(){const e=document.getElementById(\"lightbox\");e&&(e.classList.remove(\"active\"),document.body.style.overflow=\"auto\")}function g(){n.length>1&&(o=(o-1+n.length)%n.length,i())}function a(){n.length>1&&(o=(o+1)%n.length,i())}function i(){const e=document.getElementById(\"lightboxImage\"),t=document.getElementById(\"imageCounter\");e&&t&&(e.src=n[o],e.alt=`Image ${o+1}`,t.textContent=`${o+1} / ${n.length}`)}document.addEventListener(\"keydown\",function(e){const t=document.getElementById(\"lightbox\");if(t&&t.classList.contains(\"active\"))switch(e.key){case\"Escape\":c();break;case\"ArrowLeft\":g();break;case\"ArrowRight\":a();break}});document.addEventListener(\"click\",function(e){const t=document.getElementById(\"lightbox\");e.target===t&&c()});"]],"assets":["/_astro/about.Bqqpg7nS.css","/favicon.svg","/logo.svg","/og-image.jpg","/placeholder-image.svg","/robots.txt","/_worker.js/index.js","/_worker.js/renderers.mjs","/_worker.js/<EMAIL>","/_worker.js/_astro-internal_middleware.mjs","/_worker.js/_noop-actions.mjs","/_worker.js/pages/about.astro.mjs","/_worker.js/pages/index.astro.mjs","/_worker.js/pages/privacy.astro.mjs","/_worker.js/pages/products.astro.mjs","/_worker.js/pages/success.astro.mjs","/_worker.js/pages/terms.astro.mjs","/_worker.js/pages/_image.astro.mjs","/_worker.js/chunks/astro-designed-error-pages_Bl8yq204.mjs","/_worker.js/chunks/astro_vVsu7Htd.mjs","/_worker.js/chunks/cloudflare-kv-binding_DMly_2Gl.mjs","/_worker.js/chunks/index_ByGjsA6F.mjs","/_worker.js/chunks/index_C4LTQoxk.mjs","/_worker.js/chunks/Layout_4yv3UG1S.mjs","/_worker.js/chunks/noop-middleware_DQJXsadJ.mjs","/_worker.js/chunks/path_h5kZAkfu.mjs","/_worker.js/chunks/polar_BC0MPE1-.mjs","/_worker.js/chunks/ProductCard_BP_0FcaG.mjs","/_worker.js/chunks/sdk_DEQ9AU5A.mjs","/_worker.js/chunks/server_BXuq8sJY.mjs","/_worker.js/chunks/sharp_D1_czOH9.mjs","/_worker.js/chunks/StructuredData_C48DQqlT.mjs","/_worker.js/_astro/about.Bqqpg7nS.css","/_worker.js/pages/api/checkout.astro.mjs","/_worker.js/pages/api/products.astro.mjs","/_worker.js/pages/api/webhooks.astro.mjs","/_worker.js/pages/products/_slug_.astro.mjs","/_worker.js/chunks/astro/server_oCxQtzs6.mjs","/about/index.html","/privacy/index.html","/products/index.html","/success/index.html","/terms/index.html","/index.html"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"xGDTsqzhUWQCho4GBWwIP+GAnd08hZLCTqEFtc796Nc=","sessionConfig":{"driver":"cloudflare-kv-binding","options":{"binding":"SESSION"}}});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = () => import('./chunks/cloudflare-kv-binding_DMly_2Gl.mjs');

export { manifest };
