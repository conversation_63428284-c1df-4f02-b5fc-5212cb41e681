---
import type { HTMLAttributes } from "astro/types";

const { props } = Astro;

type Link = HTMLAttributes<"link">;
---

{props.extend.link?.map((attributes: Link) => <link {...attributes} />)}
{
  props.extend.meta?.map(({ content, httpEquiv, media, name, property }) => (
    <meta
      name={name}
      property={property}
      content={content}
      http-equiv={httpEquiv}
      media={media}
    />
  ))
}
