/*!
 * Sitemap
 * Copyright(c) 2011 <PERSON>
 * MIT Licensed
 */
export { SitemapItemStream, SitemapItemStreamOptions, } from './lib/sitemap-item-stream';
export { IndexTagNames, SitemapIndexStream, SitemapIndexStreamOptions, SitemapAndIndexStream, SitemapAndIndexStreamOptions, } from './lib/sitemap-index-stream';
export { streamToPromise, SitemapStream, SitemapStreamOptions, } from './lib/sitemap-stream';
export * from './lib/errors';
export * from './lib/types';
export { lineSeparatedURLsToSitemapOptions, mergeStreams, validateSMIOptions, normalizeURL, ReadlineStream, ReadlineStreamOptions, } from './lib/utils';
export { xmlLint } from './lib/xmllint';
export { parseSitemap, XMLToSitemapItemStream, XMLToSitemapItemStreamOptions, ObjectStreamToJSON, ObjectStreamToJSONOptions, } from './lib/sitemap-parser';
export { parseSitemapIndex, XMLToSitemapIndexStream, XMLToSitemapIndexItemStreamOptions, IndexObjectStreamToJSON, IndexObjectStreamToJSONOptions, } from './lib/sitemap-index-parser';
export { simpleSitemapAndIndex } from './lib/sitemap-simple';
